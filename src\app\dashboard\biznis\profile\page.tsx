'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import {
  getProfile,
  updateProfile,
  getBusiness,
  updateBusiness,
} from '@/lib/profiles';
import {
  Loader2,
  Save,
  Building,
  Globe,
  Users,
  DollarSign,
} from 'lucide-react';
import { toast } from 'sonner';

const profileSchema = z.object({
  username: z.string().min(3, 'Username mora imati najmanje 3 karaktera'),
  bio: z
    .string()
    .max(500, 'Bio može imati maksimalno 500 karaktera')
    .optional(),
  location: z.string().optional(),
  website_url: z
    .string()
    .url('Neispravna URL adresa')
    .optional()
    .or(z.literal('')),
  company_name: z
    .string()
    .min(2, 'Naziv kompanije mora imati najmanje 2 karaktera'),
  industry: z.string().optional(),
  company_size: z.string().optional(),
  budget_range: z.string().optional(),
});

type ProfileForm = z.infer<typeof profileSchema>;

const industries = [
  'Tehnologija',
  'Moda i ljepota',
  'Hrana i piće',
  'Putovanja',
  'Fitness i zdravlje',
  'Obrazovanje',
  'Finansije',
  'Nekretnine',
  'Automobili',
  'Sport',
  'Zabava',
  'Ostalo',
];

const companySizes = [
  '1-10 zaposlenih',
  '11-50 zaposlenih',
  '51-200 zaposlenih',
  '201-500 zaposlenih',
  '500+ zaposlenih',
];

const budgetRanges = [
  'Do 1.000 KM',
  '1.000 - 5.000 KM',
  '5.000 - 10.000 KM',
  '10.000 - 25.000 KM',
  '25.000+ KM',
];

export default function BusinessProfilePage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const [business, setBusiness] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
  });

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load profile
      const { data: profileData, error: profileError } = await getProfile(
        user!.id
      );
      if (profileError || !profileData) {
        toast.error('Greška pri učitavanju profila');
        return;
      }
      setProfile(profileData);

      // Load business data
      const { data: businessData, error: businessError } = await getBusiness(
        user!.id
      );
      if (businessError || !businessData) {
        toast.error('Greška pri učitavanju business podataka');
        return;
      }
      setBusiness(businessData);

      // Popuni formu sa postojećim podacima
      reset({
        username: profileData.username || '',
        bio: profileData.bio || '',
        location: profileData.location || '',
        website_url: profileData.website_url || '',
        company_name: businessData.company_name || '',
        industry: businessData.industry || '',
        company_size: businessData.company_size || '',
        budget_range: businessData.budget_range || '',
      });
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ProfileForm) => {
    if (!user) return;

    setSaving(true);
    try {
      // Update profile
      const { error: profileError } = await updateProfile(user.id, {
        username: data.username,
        bio: data.bio || null,
        location: data.location || null,
        website_url: data.website_url || null,
      });

      if (profileError) {
        if (profileError.message.includes('username')) {
          toast.error('Username je već zauzet');
        } else {
          toast.error('Greška pri ažuriranju profila');
        }
        return;
      }

      // Update business data
      const { error: businessError } = await updateBusiness(user.id, {
        company_name: data.company_name,
        industry: data.industry || null,
        company_size: data.company_size || null,
        budget_range: data.budget_range || null,
      });

      if (businessError) {
        toast.error('Greška pri ažuriranju business podataka');
        return;
      }

      toast.success('Profil je uspješno ažuriran');
      loadData(); // Refresh data
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Postavke profila</h1>
          <p className="text-muted-foreground mt-2">
            Upravljajte svojim javnim profilom koji vide influenceri
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Osnovne informacije */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <CardTitle>Osnovne informacije</CardTitle>
              </div>
              <CardDescription>
                Ovi podaci se prikazuju na vašem javnom profilu
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="username">Username *</Label>
                  <Input
                    id="username"
                    {...register('username')}
                    placeholder="@vasabrand"
                  />
                  {errors.username && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.username.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="company_name">Naziv kompanije *</Label>
                  <Input
                    id="company_name"
                    {...register('company_name')}
                    placeholder="Vaša kompanija d.o.o."
                  />
                  {errors.company_name && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.company_name.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="bio">Opis kompanije</Label>
                <Textarea
                  id="bio"
                  {...register('bio')}
                  placeholder="Opišite vašu kompaniju i čime se bavite..."
                  rows={4}
                />
                {errors.bio && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.bio.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="location">Lokacija</Label>
                  <Input
                    id="location"
                    {...register('location')}
                    placeholder="Sarajevo, BiH"
                  />
                </div>

                <div>
                  <Label htmlFor="website_url">Website</Label>
                  <Input
                    id="website_url"
                    {...register('website_url')}
                    placeholder="https://vaswebsite.com"
                  />
                  {errors.website_url && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.website_url.message}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informacije o kompaniji */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <CardTitle>Informacije o kompaniji</CardTitle>
              </div>
              <CardDescription>
                Dodatne informacije koje pomažu influencerima da vas bolje
                razumiju
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="industry">Industrija</Label>
                <Select onValueChange={value => setValue('industry', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Izaberite industriju" />
                  </SelectTrigger>
                  <SelectContent>
                    {industries.map(industry => (
                      <SelectItem key={industry} value={industry}>
                        {industry}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="company_size">Veličina kompanije</Label>
                <Select
                  onValueChange={value => setValue('company_size', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Izaberite veličinu kompanije" />
                  </SelectTrigger>
                  <SelectContent>
                    {companySizes.map(size => (
                      <SelectItem key={size} value={size}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Budget informacije */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <CardTitle>Budget informacije</CardTitle>
              </div>
              <CardDescription>
                Ove informacije pomažu influencerima da razumiju vaš budget
                opseg
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="budget_range">Tipični budget opseg</Label>
                <Select
                  onValueChange={value => setValue('budget_range', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Izaberite budget opseg" />
                  </SelectTrigger>
                  <SelectContent>
                    {budgetRanges.map(range => (
                      <SelectItem key={range} value={range}>
                        {range}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Sačuvaj promjene
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
