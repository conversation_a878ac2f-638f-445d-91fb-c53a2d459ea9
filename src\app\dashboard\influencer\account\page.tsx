'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { getProfile, updateProfile } from '@/lib/profiles';
import { Loader2, Save, Shield, User, CreditCard, Mail } from 'lucide-react';
import { toast } from 'sonner';

const accountSchema = z.object({
  full_name: z.string().min(2, 'Ime mora imati najmanje 2 karaktera'),
  email: z.string().email('Neispravna email adresa'),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  postal_code: z.string().optional(),
  country: z.string().optional(),
  tax_id: z.string().optional(),
  bank_account: z.string().optional(),
  bank_name: z.string().optional(),
});

type AccountForm = z.infer<typeof accountSchema>;

export default function InfluencerAccountPage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<AccountForm>({
    resolver: zodResolver(accountSchema),
  });

  useEffect(() => {
    if (user) {
      loadProfile();
    }
  }, [user]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const { data, error } = await getProfile(user!.id);

      if (error || !data) {
        toast.error('Greška pri učitavanju profila');
        return;
      }

      setProfile(data);

      // Popuni formu sa postojećim podacima
      reset({
        full_name: data.full_name || '',
        email: user!.email || '',
        phone: data.phone || '',
        address: data.address || '',
        city: data.city || '',
        postal_code: data.postal_code || '',
        country: data.country || 'Bosna i Hercegovina',
        tax_id: data.tax_id || '',
        bank_account: data.bank_account || '',
        bank_name: data.bank_name || '',
      });
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: AccountForm) => {
    if (!user) return;

    setSaving(true);
    try {
      const { error } = await updateProfile(user.id, {
        full_name: data.full_name,
        phone: data.phone || null,
        address: data.address || null,
        city: data.city || null,
        postal_code: data.postal_code || null,
        country: data.country || null,
        tax_id: data.tax_id || null,
        bank_account: data.bank_account || null,
        bank_name: data.bank_name || null,
      });

      if (error) {
        toast.error('Greška pri ažuriranju podataka');
        return;
      }

      toast.success('Podaci su uspješno ažurirani');
      loadProfile(); // Refresh data
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Moj račun</h1>
          <p className="text-muted-foreground mt-2">
            Upravljajte svojim ličnim podacima i sigurnosnim postavkama
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Lični podaci */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <CardTitle>Lični podaci</CardTitle>
              </div>
              <CardDescription>
                Ovi podaci su privatni i neće biti prikazani javno
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="full_name">Puno ime *</Label>
                  <Input
                    id="full_name"
                    {...register('full_name')}
                    placeholder="Vaše puno ime"
                  />
                  {errors.full_name && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.full_name.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="email">Email adresa *</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    disabled
                    className="bg-muted"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Email se ne može mijenjati
                  </p>
                </div>

                <div>
                  <Label htmlFor="phone">Telefon</Label>
                  <Input
                    id="phone"
                    {...register('phone')}
                    placeholder="+387 XX XXX XXX"
                  />
                </div>

                <div>
                  <Label htmlFor="tax_id">JMB/JMBG</Label>
                  <Input
                    id="tax_id"
                    {...register('tax_id')}
                    placeholder="1234567890123"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Adresa */}
          <Card>
            <CardHeader>
              <CardTitle>Adresa</CardTitle>
              <CardDescription>
                Potrebno za izdavanje računa i plaćanja
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="address">Ulica i broj</Label>
                <Input
                  id="address"
                  {...register('address')}
                  placeholder="Ulica i broj"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="city">Grad</Label>
                  <Input
                    id="city"
                    {...register('city')}
                    placeholder="Sarajevo"
                  />
                </div>

                <div>
                  <Label htmlFor="postal_code">Poštanski broj</Label>
                  <Input
                    id="postal_code"
                    {...register('postal_code')}
                    placeholder="71000"
                  />
                </div>

                <div>
                  <Label htmlFor="country">Država</Label>
                  <Input
                    id="country"
                    {...register('country')}
                    placeholder="Bosna i Hercegovina"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Banking podaci */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <CreditCard className="h-5 w-5" />
                <CardTitle>Banking podaci</CardTitle>
              </div>
              <CardDescription>Potrebno za isplate zarada</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="bank_name">Naziv banke</Label>
                  <Input
                    id="bank_name"
                    {...register('bank_name')}
                    placeholder="UniCredit Bank"
                  />
                </div>

                <div>
                  <Label htmlFor="bank_account">Broj računa</Label>
                  <Input
                    id="bank_account"
                    {...register('bank_account')}
                    placeholder="****************"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sigurnost */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <CardTitle>Sigurnost</CardTitle>
              </div>
              <CardDescription>
                Upravljanje sigurnosnim postavkama
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" type="button">
                <Mail className="h-4 w-4 mr-2" />
                Promijeni lozinku
              </Button>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Sačuvaj promjene
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
