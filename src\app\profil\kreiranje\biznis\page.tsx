'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Loader2, Building2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { upsertProfile, createBusiness } from '@/lib/profiles';

const businessSchema = z.object({
  username: z
    .string()
    .min(3, 'Username mora imati najmanje 3 karaktera')
    .max(20, 'Username može imati maksimalno 20 karaktera')
    .regex(
      /^[a-zA-Z0-9_]+$/,
      'Username može sadrž<PERSON>ti samo slova, brojeve i _'
    ),
  companyName: z
    .string()
    .min(2, 'Naziv firme mora imati najmanje 2 karaktera')
    .max(200, 'Naziv firme može imati maksimalno 200 karaktera'),
  bio: z
    .string()
    .max(500, 'Bio može imati maksimalno 500 karaktera')
    .optional(),
  location: z
    .string()
    .max(100, 'Lokacija može imati maksimalno 100 karaktera')
    .optional(),
  website: z
    .string()
    .url('Unesite validnu URL adresu')
    .optional()
    .or(z.literal('')),
  industry: z
    .string()
    .max(100, 'Industrija može imati maksimalno 100 karaktera')
    .optional(),
  companySize: z
    .enum(['1-10', '11-50', '51-200', '201-500', '500+'], {
      errorMap: () => ({ message: 'Izaberite veličinu firme' }),
    })
    .optional(),
  budgetRange: z
    .enum(['500-2000', '2000-5000', '5000-10000', '10000+'], {
      errorMap: () => ({ message: 'Izaberite budžet raspon' }),
    })
    .optional(),
});

type BusinessForm = z.infer<typeof businessSchema>;

export default function BiznisProfilPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<BusinessForm>({
    resolver: zodResolver(businessSchema),
  });

  const onSubmit = async (data: BusinessForm) => {
    if (!user) return;

    setIsLoading(true);

    try {
      // Create or update basic profile
      const { error: profileError } = await upsertProfile(user.id, {
        username: data.username,
        bio: data.bio || null,
        location: data.location || null,
        website_url: data.website || null,
        user_type: 'business',
      });

      if (profileError) {
        if (profileError.message.includes('username')) {
          setError('username', { message: 'Username je već zauzet' });
        } else {
          setError('root', { message: 'Greška pri ažuriranju profila' });
        }
        return;
      }

      // Create business specific data
      const { error: businessError } = await createBusiness({
        id: user.id,
        company_name: data.companyName,
        industry: data.industry || null,
        company_size: data.companySize || null,
        budget_range: data.budgetRange || null,
      });

      if (businessError) {
        setError('root', { message: 'Greška pri kreiranju biznis profila' });
        return;
      }

      // Redirect to dashboard
      router.push('/dashboard/biznis');
    } catch (error) {
      setError('root', { message: 'Neočekivana greška. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/profil/kreiranje"
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">
                🔗
              </span>
            </div>
            <span className="text-xl font-bold text-foreground">
              InfluConnect
            </span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 px-4 py-8">
        <div className="container mx-auto max-w-2xl">
          <Card>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Building2 className="h-8 w-8 text-primary" />
              </div>
              <CardTitle className="text-2xl">Biznis Profil</CardTitle>
              <CardDescription>
                Popunite informacije o vašoj firmi da biste završili profil
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Basic Info */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Osnovne informacije</h3>

                  <div className="space-y-2">
                    <Label htmlFor="username">Username *</Label>
                    <Input
                      id="username"
                      placeholder="moja_firma"
                      {...register('username')}
                      className={errors.username ? 'border-red-500' : ''}
                    />
                    {errors.username && (
                      <p className="text-sm text-red-500">
                        {errors.username.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="companyName">Naziv firme *</Label>
                    <Input
                      id="companyName"
                      placeholder="Moja Firma d.o.o."
                      {...register('companyName')}
                      className={errors.companyName ? 'border-red-500' : ''}
                    />
                    {errors.companyName && (
                      <p className="text-sm text-red-500">
                        {errors.companyName.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Opis firme</Label>
                    <Textarea
                      id="bio"
                      placeholder="Opišite vašu firmu, proizvode ili usluge..."
                      rows={3}
                      {...register('bio')}
                      className={errors.bio ? 'border-red-500' : ''}
                    />
                    {errors.bio && (
                      <p className="text-sm text-red-500">
                        {errors.bio.message}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="location">Lokacija</Label>
                      <Input
                        id="location"
                        placeholder="Sarajevo, BiH"
                        {...register('location')}
                        className={errors.location ? 'border-red-500' : ''}
                      />
                      {errors.location && (
                        <p className="text-sm text-red-500">
                          {errors.location.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        placeholder="https://mojafirma.ba"
                        {...register('website')}
                        className={errors.website ? 'border-red-500' : ''}
                      />
                      {errors.website && (
                        <p className="text-sm text-red-500">
                          {errors.website.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Company Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Detalji o firmi</h3>

                  <div className="space-y-2">
                    <Label htmlFor="industry">Industrija</Label>
                    <Input
                      id="industry"
                      placeholder="IT, Moda, Hrana, Turizam..."
                      {...register('industry')}
                      className={errors.industry ? 'border-red-500' : ''}
                    />
                    {errors.industry && (
                      <p className="text-sm text-red-500">
                        {errors.industry.message}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="companySize">Veličina firme</Label>
                      <select
                        id="companySize"
                        {...register('companySize')}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Izaberite...</option>
                        <option value="1-10">1-10 zaposlenih</option>
                        <option value="11-50">11-50 zaposlenih</option>
                        <option value="51-200">51-200 zaposlenih</option>
                        <option value="201-500">201-500 zaposlenih</option>
                        <option value="500+">500+ zaposlenih</option>
                      </select>
                      {errors.companySize && (
                        <p className="text-sm text-red-500">
                          {errors.companySize.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="budgetRange">
                        Mjesečni budžet za marketing
                      </Label>
                      <select
                        id="budgetRange"
                        {...register('budgetRange')}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Izaberite...</option>
                        <option value="500-2000">500-2.000 KM</option>
                        <option value="2000-5000">2.000-5.000 KM</option>
                        <option value="5000-10000">5.000-10.000 KM</option>
                        <option value="10000+">10.000+ KM</option>
                      </select>
                      {errors.budgetRange && (
                        <p className="text-sm text-red-500">
                          {errors.budgetRange.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Kreiranje profila...
                    </>
                  ) : (
                    'Završi profil'
                  )}
                </Button>

                {/* Root Error */}
                {errors.root && (
                  <p className="text-sm text-red-500 text-center">
                    {errors.root.message}
                  </p>
                )}
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
