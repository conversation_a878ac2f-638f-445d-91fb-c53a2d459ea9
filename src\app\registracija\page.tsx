'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ArrowLeft, User, Building2 } from 'lucide-react';

export default function RegistracijaPage() {
  const router = useRouter();
  const [selectedType, setSelectedType] = useState<
    'influencer' | 'business' | null
  >(null);

  const handleContinue = () => {
    if (selectedType) {
      router.push(`/registracija/${selectedType}`);
    }
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/"
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">
                🔗
              </span>
            </div>
            <span className="text-xl font-bold text-foreground">
              InfluConnect
            </span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Registracija
            </h1>
            <p className="text-muted-foreground">
              Izaberite tip korisnika da biste nastavili
            </p>
          </div>

          <div className="space-y-4">
            {/* Influencer Card */}
            <Card
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedType === 'influencer'
                  ? 'ring-2 ring-primary border-primary'
                  : 'hover:border-primary/50'
              }`}
              onClick={() => setSelectedType('influencer')}
            >
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-xl">Influencer</CardTitle>
                <CardDescription>
                  Zarađujem kroz kreiranje sadržaja i promociju brendova
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Pronađi kampanje koje odgovaraju tvojoj niši</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Postavi svoje cijene i uslove</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Sigurno plaćanje kroz escrow sistem</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Business Card */}
            <Card
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedType === 'business'
                  ? 'ring-2 ring-primary border-primary'
                  : 'hover:border-primary/50'
              }`}
              onClick={() => setSelectedType('business')}
            >
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Building2 className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-xl">Biznis</CardTitle>
                <CardDescription>
                  Kreiram kampanje za promociju svojih proizvoda ili usluga
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Kreiraj kampanje za svaki budžet</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Biraj između stotina lokalnih influencera</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span>Transparentno praćenje rezultata</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Continue Button */}
          <Button
            onClick={handleContinue}
            disabled={!selectedType}
            className="w-full"
            size="lg"
          >
            Nastavi →
          </Button>

          {/* Login Link */}
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              Već imate nalog?{' '}
              <Link
                href="/prijava"
                className="text-primary hover:underline font-medium"
              >
                Prijavite se
              </Link>
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
