'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getProfile } from '@/lib/profiles';
import { ResponsiveNavigation } from '@/components/navigation/ResponsiveNavigation';
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown';
import { Loader2 } from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  requiredUserType?: 'influencer' | 'business';
  hideHeader?: boolean;
}

export function DashboardLayout({
  children,
  requiredUserType,
  hideHeader = false,
}: DashboardLayoutProps) {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/prijava');
      return;
    }

    loadProfile();
  }, [user, authLoading, router]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const { data, error } = await getProfile(user!.id);

      if (error) {
        console.error('Profile loading error:', error);
        if (error.message && error.message.includes('No rows')) {
          router.push('/profil/kreiranje');
          return;
        }
        setError('Greška pri učitavanju profila');
        return;
      }

      if (!data) {
        router.push('/profil/kreiranje');
        return;
      }

      // Check if profile is completed
      if (!data.profile_completed) {
        if (data.user_type === 'influencer') {
          router.push('/profil/kreiranje/influencer/onboarding');
        } else if (data.user_type === 'business') {
          router.push('/profil/kreiranje/biznis');
        } else {
          router.push('/profil/kreiranje');
        }
        return;
      }

      setProfile(data);

      // Provjeri da li korisnik ima pravo pristupa ovoj stranici
      if (requiredUserType && data.user_type !== requiredUserType) {
        // Preusmjeri na odgovarajući dashboard
        if (data.user_type === 'influencer') {
          router.push('/dashboard/influencer');
        } else if (data.user_type === 'business') {
          router.push('/dashboard/biznis');
        }
        return;
      }
    } catch (err) {
      console.error('Unexpected error in loadProfile:', err);
      setError('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Učitavanje...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">Greška</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Pokušaj ponovo
          </button>
        </div>
      </div>
    );
  }

  // No profile state
  if (!profile) {
    return null; // Router redirect will handle this
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Responsive Navigation */}
      <ResponsiveNavigation userType={profile.user_type} />

      {/* Main Content - full width without sidebar */}
      <div className="flex flex-col min-h-screen">
        {/* Header */}
        {!hideHeader && (
          <header className="bg-card border-b border-border px-6 py-4 md:pl-20">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">
                {profile.user_type === 'influencer'
                  ? 'Influencer Dashboard'
                  : 'Biznis Dashboard'}
              </h1>
              <p className="text-muted-foreground">
                Dobrodošli, {profile.full_name || profile.username}
              </p>
            </div>

            {/* User Info */}
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              {profile.avatar_url && (
                <img
                  src={profile.avatar_url}
                  alt={profile.username}
                  className="w-8 h-8 rounded-full object-cover"
                />
              )}
              <div className="text-right hidden sm:block">
                <p className="text-sm font-medium text-foreground">
                  {profile.full_name || profile.username}
                </p>
                <p className="text-xs text-muted-foreground">
                  @{profile.username}
                </p>
              </div>
            </div>
          </div>
          </header>
        )}

        {/* Page Content */}
        <main className="flex-1 overflow-auto pb-16 md:pb-0">
          <div className="p-6">{children}</div>
        </main>
      </div>
    </div>
  );
}
