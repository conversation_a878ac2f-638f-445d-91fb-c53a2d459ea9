'use client';

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Rating } from '@/components/ui/rating';
import { MapPin, Users, Verified } from 'lucide-react';
import Link from 'next/link';
import type { InfluencerSearchResult } from '@/lib/marketplace';

interface InfluencerCardProps {
  influencer: InfluencerSearchResult;
}

export function InfluencerCard({ influencer }: InfluencerCardProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatPrice = (price: number) => {
    return `${price.toLocaleString()} KM`;
  };

  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <Link href={`/influencer/${influencer.username}`}>
      <Card className="group hover:shadow-lg transition-all duration-200 cursor-pointer overflow-hidden border-0 shadow-sm hover:shadow-md">
        {/* Header with Avatar and Rating */}
        <CardHeader className="relative p-0">
          {/* Background gradient */}
          <div className="h-32 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent" />
          
          {/* Rating in top-right corner */}
          <div className="absolute top-3 right-3">
            <div className="bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 shadow-sm">
              <Rating
                rating={influencer.average_rating}
                totalReviews={influencer.total_reviews}
                size="sm"
                showReviewCount={false}
              />
            </div>
          </div>

          {/* Avatar positioned over the gradient */}
          <div className="absolute -bottom-8 left-6">
            <Avatar className="h-16 w-16 border-4 border-white shadow-lg">
              <AvatarImage src={influencer.avatar_url} />
              <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                {getInitials(influencer.full_name || influencer.username)}
              </AvatarFallback>
            </Avatar>
          </div>
        </CardHeader>

        <CardContent className="pt-10 pb-6 px-6">
          {/* Name and verification */}
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-semibold text-lg truncate group-hover:text-primary transition-colors">
              {influencer.full_name || influencer.username}
            </h3>
            {influencer.is_verified && (
              <Verified className="h-4 w-4 text-blue-500 fill-current flex-shrink-0" />
            )}
          </div>

          {/* Username */}
          <p className="text-sm text-muted-foreground mb-2">
            @{influencer.username}
          </p>

          {/* Location */}
          {influencer.location && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground mb-3">
              <MapPin className="h-3 w-3" />
              {influencer.location}
            </div>
          )}

          {/* Bio */}
          {influencer.bio && (
            <p className="text-sm text-muted-foreground mb-4 line-clamp-2 leading-relaxed">
              {influencer.bio}
            </p>
          )}

          {/* Categories */}
          {influencer.categories.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-4">
              {influencer.categories.slice(0, 2).map((category, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs px-2 py-1"
                >
                  {category}
                </Badge>
              ))}
              {influencer.categories.length > 2 && (
                <Badge variant="outline" className="text-xs px-2 py-1">
                  +{influencer.categories.length - 2}
                </Badge>
              )}
            </div>
          )}

          {/* Stats Row */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Users className="h-4 w-4" />
              <span className="font-medium">{formatFollowers(influencer.total_followers)}</span>
              <span className="text-xs">pratilaca</span>
            </div>
            {influencer.min_price > 0 && (
              <div className="text-right">
                <div className="text-xs text-muted-foreground">od</div>
                <div className="font-bold text-primary text-lg">
                  {formatPrice(influencer.min_price)}
                </div>
              </div>
            )}
          </div>

          {/* Pricing Packages Preview */}
          {influencer.pricing && influencer.pricing.length > 0 && (
            <div className="mb-4">
              <div className="text-xs text-muted-foreground font-medium mb-2">Dostupni paketi:</div>
              <div className="grid grid-cols-1 gap-2">
                {influencer.pricing.slice(0, 2).map((pkg, index) => (
                  <div key={index} className="flex items-center justify-between bg-muted/30 rounded-lg px-3 py-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm">
                        {influencer.platforms.find(p => p.platform_id === pkg.platform_id)?.platform_icon}
                      </span>
                      <span className="text-xs font-medium">{pkg.content_type_name}</span>
                    </div>
                    <span className="text-sm font-bold text-primary">
                      {formatPrice(pkg.price)}
                    </span>
                  </div>
                ))}
                {influencer.pricing.length > 2 && (
                  <div className="text-xs text-muted-foreground text-center py-1">
                    +{influencer.pricing.length - 2} više paketa
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Platforms */}
          {influencer.platforms.length > 0 && (
            <div className="flex items-center gap-2 pt-3 border-t border-border/50">
              <span className="text-xs text-muted-foreground font-medium">Platforme:</span>
              <div className="flex gap-1">
                {influencer.platforms.slice(0, 4).map((platform, index) => (
                  <span
                    key={index}
                    className="text-lg hover:scale-110 transition-transform"
                    title={platform.platform_name}
                  >
                    {platform.platform_icon}
                  </span>
                ))}
                {influencer.platforms.length > 4 && (
                  <span className="text-xs text-muted-foreground self-center">
                    +{influencer.platforms.length - 4}
                  </span>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  );
}
