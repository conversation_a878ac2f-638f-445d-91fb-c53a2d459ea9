'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface CityStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext: () => void;
  onBack: () => void;
}

export function CityStep({ value, onChange, onNext, onBack }: CityStepProps) {
  const handleNext = () => {
    onNext();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          U kom gradu živite?
        </h2>
        <p className="text-gray-600">
          Ovo polje je opcionalno, ali pomaže brendovima da pronađu lokalne influencere
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="city">Grad (opcionalno)</Label>
          <Input
            id="city"
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder="npr. Beograd, Zagreb, Sarajevo..."
            maxLength={100}
          />
        </div>

        <div className="text-sm text-gray-500">
          <p>Možete preskočiti ovaj korak ako ne želite da delite lokaciju.</p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex-1"
        >
          Nazad
        </Button>
        <Button
          onClick={handleNext}
          className="flex-1"
        >
          Dalje
        </Button>
      </div>
    </div>
  );
}
