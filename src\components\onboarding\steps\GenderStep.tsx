'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface GenderStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext: () => void;
  onBack: () => void;
}

export function GenderStep({ value, onChange, onNext, onBack }: GenderStepProps) {
  const genderOptions = [
    { value: 'musko', label: '<PERSON><PERSON><PERSON>' },
    { value: 'zensko', label: '<PERSON><PERSON><PERSON>' },
    { value: 'ostalo', label: 'Ostalo' },
  ];

  const handleNext = () => {
    if (value) {
      onNext();
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-center">Pol</CardTitle>
        <p className="text-center text-muted-foreground">
          Izaberite svoj pol
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {genderOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => onChange(option.value)}
              className={`w-full p-4 text-left border rounded-lg transition-colors ${
                value === option.value
                  ? 'border-primary bg-primary/5 text-primary'
                  : 'border-border hover:border-primary/50'
              }`}
            >
              <div className="flex items-center justify-between">
                <span className="font-medium">{option.label}</span>
                {value === option.value && (
                  <div className="w-4 h-4 rounded-full bg-primary flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-white" />
                  </div>
                )}
              </div>
            </button>
          ))}
        </div>

        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            className="flex-1"
          >
            Nazad
          </Button>
          <Button
            type="button"
            onClick={handleNext}
            disabled={!value}
            className="flex-1"
          >
            Dalje
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
